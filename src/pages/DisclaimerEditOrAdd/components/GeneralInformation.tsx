import { Button, Form, Input, Switch, Typography } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React from 'react';

import ConfigurableCard from 'src/components/ConfigurableCard';
import UploadFile from 'src/components/Upload';
import style from '../style.module.scss';

const { TextArea } = Input;
const { Text } = Typography;

// 表单验证规则
export const validationRules = {
  disclaimerName: [
    { required: true, message: 'Please enter disclaimer name' },
    { max: 100, message: 'Disclaimer name cannot exceed 100 characters' },
    { whitespace: true, message: 'Disclaimer name cannot be empty' },
  ],
  disclaimerDescription: [
    { required: true, message: 'Please enter disclaimer description' },
    { max: 1000, message: 'Description cannot exceed 1000 characters' },
    { whitespace: true, message: 'Description cannot be empty' },
  ],
};

// 可复用的表单字段组件
interface FormFieldProps {
  label: string;
  name: string;
  required?: boolean;
  rules?: any[];
  children: React.ReactNode;
}

const FormField: React.FC<FormFieldProps> = ({ label, name, required = false, rules = [], children }) => (
  <div className={style.formField}>
    <Text strong>
      {label} {required && '*'}
    </Text>
    <div style={{ marginTop: 8 }}>
      <Form.Item name={name} rules={rules} style={{ marginBottom: 0 }}>
        {children}
      </Form.Item>
    </div>
  </div>
);

interface GeneralInformationProps {
  currentStep: 'general' | 'advanced';
  isEdit: boolean;
  loading: boolean;
  onSave: (form: FormInstance) => void;
  onCancel: () => void;
  onFileUpload: (data?: { hash?: string; filename?: string }) => void;
  onFormChange?: (form: FormInstance) => void;
  initialValues?: any;
}

const GeneralInformation: React.FC<GeneralInformationProps> = ({
  loading,
  onSave,
  onFileUpload,
  onFormChange,
  initialValues = {
    name: '',
    description: '',
    status: true,
    legalDocument: undefined,
  },
}) => {
  const [form] = Form.useForm();

  // 监听表单值变化
  const statusValue = Form.useWatch('status', form);

  // 当表单值变化时通知父组件
  React.useEffect(() => {
    if (onFormChange) {
      onFormChange(form);
    }
  }, [form, onFormChange]);

  const handleSave = () => {
    onSave(form);
  };

  return (
    <ConfigurableCard
      header={{
        title: 'General Information',
        extra: (
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Button onClick={handleSave} loading={loading}>
              Save
            </Button>
          </div>
        )
      }}
      className={style.sectionCard}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
      >
        <>
          <FormField
            label="Disclaimer Name"
            name="name"
            required
            rules={validationRules.disclaimerName}
          >
            <Input placeholder="Enter disclaimer name" />
          </FormField>

          <FormField
            label="Disclaimer Description"
            name="description"
            required
            rules={validationRules.disclaimerDescription}
          >
            <TextArea
              placeholder="Enter disclaimer description"
              rows={4}
            />
          </FormField>

          <div className={style.formField}>
            <Text strong>Status</Text>
            <div style={{ marginTop: 8 }}>
              <Form.Item name="status" valuePropName="checked" style={{ marginBottom: 0 }}>
                <div className={style.statusToggle}>
                  <Switch />
                  <Text className={style.statusText}>
                    {statusValue ? 'Active' : 'Inactive'}
                  </Text>
                </div>
              </Form.Item>
            </div>
          </div>

          <FormField
            label="Approval Document"
            name="legalDocument"
          >
            <UploadFile
              file={form.getFieldValue('legalDocument') || { hash: '', filename: '' }}
              onChange={onFileUpload}
              mode="edit"
            />
          </FormField>
        </>
      </Form>
    </ConfigurableCard>
  );
};

export default GeneralInformation;
